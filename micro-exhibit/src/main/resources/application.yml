spring:
  application:
    name: micro-exhibit
  threads:
    virtual:
      enabled: true
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 5MB
      enabled: true
  profiles:
    default: dev
    active: dev
  rabbitmq:
    listener:
      simple:
        acknowledge-mode: manual
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    default-property-inclusion: NON_NULL
    time-zone: GMT+8
server:
  port: 8083
  max-http-request-header-size: 10MB

logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} %5p [${spring.application.name},%X{traceId},%X{spanId}] [%t] --- %logger{36}: %m%n"

megalith:
  cache:
    queue-prefix: cache.blog.evict.queue.
    fanout-exchange: cache.blog.evict.fanout.exchange
  blog:
    highest-role: admin
    blog-page-size: 5