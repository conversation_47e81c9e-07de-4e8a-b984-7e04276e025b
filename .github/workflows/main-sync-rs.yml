name: Axum build Share-Rs
on:
  push:
    branches:
      - 'main'
    paths:
      - 'micro-sync-rs/**'
      - '.github/workflows/main-sync-rs.yml'
      - 'cargo.toml'

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
        
      - name: Set up Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          toolchain: stable
          targets: x86_64-unknown-linux-musl
      - name: Run tests
        run: cargo test --release

  build:
    name: Build
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
          
      - name: Build Docker image
        run: docker build -t mingchiuli/megalith-micro-sync-rs:latest micro-sync-rs/.

      - name: Log in to DockerHub
        run: echo "${{ secrets.DOCKERHUB_PWD }}" | docker login -u "${{ secrets.DOCKERHUB_USERNAME }}" --password-stdin

      - name: Push Docker image
        run: docker push mingchiuli/megalith-micro-sync-rs
  
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    needs:
      - build
    steps:
      - name: Deploy
        uses: appleboy/ssh-action@v1.1.0
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          port: ${{ secrets.PORT }}
          script: |
            docker pull mingchiuli/megalith-micro-sync-rs:latest
            container_ids=$(docker ps --filter "name=micro-sync-rs" -q | awk '{print $1}')
            if [ -n "$container_ids" ]; then
              for container_id in $container_ids; do
                docker stop "$container_id"
                docker rm "$container_id"
              done
            fi
            docker compose up -d micro-sync-rs
            old=$(docker images | grep -w 'micro-sync-rs' | grep 'none' | awk '{print $3}')
            if [ -n "$old" ]; then
              docker rmi $old
            fi