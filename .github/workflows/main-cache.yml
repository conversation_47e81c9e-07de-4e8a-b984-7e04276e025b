name: Release Cache Maven Package
on:
  push:
    branches:
      - 'main'
    paths:
      - 'cache/**'
      - '.github/workflows/main-cache.yml'
jobs:
  build:
    name: publish
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Install JDK
        uses: actions/setup-java@v4
        with:
          distribution: 'graalvm'
          java-version: '24'
      - uses: gradle/actions/setup-gradle@v4
      - name: Publish
        run: ./gradlew :cache:publishAndReleaseToMavenCentral
        env:
          ORG_GRADLE_PROJECT_mavenCentralUsername: ${{ secrets.SONATYPE_USERNAME }}
          ORG_GRADLE_PROJECT_mavenCentralPassword: ${{ secrets.SONATYPE_PASSWORD }}
          ORG_GRADLE_PROJECT_signingInMemoryKey: ${{ secrets.GPG_PRIVATE_KEY }}
          ORG_GRADLE_PROJECT_signingInMemoryKeyPassword: ${{ secrets.GPG_SIGNING_PASSWORD }}
      - name: Release
        uses: softprops/action-gh-release@v2
        with:
          tag_name: 3.5.4
          token: ${{ secrets.CUSTOM_GITHUB_TOKEN }}
