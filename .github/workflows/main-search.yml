name: GraalVM build Search
on:
  push:
    branches:
      - 'main'
    paths:
      - 'micro-search/**'
      - 'build.gradle.kts'
      - 'common/**'
      - '.github/workflows/main-search.yml'
jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - name: Prepare
        uses: graalvm/setup-graalvm@v1
        with:
          java-version: '24'      # See 'Options' section below for all supported versions
          distribution: 'graalvm' # See 'Options' section below for all available distributions
          github-token: ${{ secrets.GITHUB_TOKEN }}
      - name: Print Info
        run: |
          echo "GRAALVM_HOME: $GRAALVM_HOME"
          echo "JAVA_HOME: $JAVA_HOME"
          java --version
          native-image --version
      - name: Build
        run: ./gradlew :micro-search:bootBuildImage
        env:
          DOCKER_USERNAME: ${{ secrets.DOCKERHUB_USERNAME }}
          DOCKER_PWD: ${{ secrets.DOCKERHUB_PWD }}
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    needs:
      - build
    steps:
      - name: Deploy
        uses: appleboy/ssh-action@v1.1.0
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          port: ${{ secrets.PORT }}
          script: |
            docker pull mingchiuli/megalith-micro-search:latest
            container_ids=$(docker ps --filter "name=micro-search" -q | awk '{print $1}')
            if [ -n "$container_ids" ]; then
              for container_id in $container_ids; do
                docker stop "$container_id"
                docker rm "$container_id"
              done
            fi
            docker compose up -d micro-search
            old=$(docker images | grep -w 'micro-search' | grep 'none' | awk '{print $3}')
            if [ -n "$old" ]; then
              docker rmi $old
            fi
