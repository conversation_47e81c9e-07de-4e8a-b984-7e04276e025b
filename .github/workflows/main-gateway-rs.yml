name: Axum build Gateway-Rs
on:
  push:
    branches:
      - 'main'
    paths:
      - 'micro-gateway-rs/**'
      - '.github/workflows/main-gateway-rs.yml'
      - 'cargo.toml'

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
        
      - name: Set up Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          toolchain: stable
          targets: x86_64-unknown-linux-musl
      - name: Run tests
        run: cargo test --release

  build:
    name: Build
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
          
      - name: Build Docker image
        run: docker build -t mingchiuli/megalith-micro-gateway-rs:latest micro-gateway-rs/.

      - name: Log in to DockerHub
        run: echo "${{ secrets.DOCKERHUB_PWD }}" | docker login -u "${{ secrets.DOCKERHUB_USERNAME }}" --password-stdin

      - name: Push Docker image
        run: docker push mingchiuli/megalith-micro-gateway-rs
  
  deploy:
    name: Deploy
    runs-on: ubuntu-latest
    needs:
      - build
    steps:
      - name: Deploy
        uses: appleboy/ssh-action@v1.1.0
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          password: ${{ secrets.PASSWORD }}
          port: ${{ secrets.PORT }}
          script: |
            docker pull mingchiuli/megalith-micro-gateway-rs:latest
            container_ids=$(docker ps --filter "name=micro-gateway-rs" -q | awk '{print $1}')
            if [ -n "$container_ids" ]; then
              for container_id in $container_ids; do
                docker stop "$container_id"
                docker rm "$container_id"
              done
            fi
            docker compose -f docker-compose.infrastructure.yml up -d micro-gateway-rs
            old=$(docker images | grep -w 'micro-gateway-rs' | grep 'none' | awk '{print $3}')
            if [ -n "$old" ]; then
              docker rmi $old
            fi
            nginx=$(docker ps | grep -w 'nginx' | awk '{print $1}')
            if [ -n "$nginx" ]; then
              docker exec $nginx nginx -s reload
            fi
