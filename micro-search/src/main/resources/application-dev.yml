spring:
  ssl:
    bundle:
      pem:
        elasticsearch-trust:
          truststore:
            certificate: "/Users/<USER>/Config/megalith/elasticsearch/certs/ca/ca.crt"
  elasticsearch:
    username: elastic
    password: 123456
    uris: https://127.0.0.1:9200
    restclient:
      ssl:
        bundle: elasticsearch-trust
  rabbitmq:
    username: guest
    password: guest
    host: 127.0.0.1
    port: 5672


megalith:
  blog:
    auth-url: http://127.0.0.1:8081/inner
    blog-url: http://127.0.0.1:8082/inner
