spring:
  application:
    name: micro-search
  threads:
    virtual:
      enabled: true
  profiles:
    default: dev
    active: dev
  rabbitmq:
    listener:
      simple:
        acknowledge-mode: manual
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    default-property-inclusion: NON_NULL
    time-zone: GMT+8
server:
  port: 8085
  max-http-request-header-size: 10MB

logging:
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} %5p [${spring.application.name},%X{traceId},%X{spanId}] [%t] --- %logger{36}: %m%n"

megalith:
  blog:
    highest-role: admin
    blog-page-size: 5