spring:
  jpa:
    show-sql: false
  datasource:
    driver-class-name: org.mariadb.jdbc.Driver
    url: ***********************************************************************************************
    username: root
    password: 123456
  rabbitmq:
    username: guest
    password: guest
    host: 127.0.0.1
    port: 5672
  data:
    redis:
      port: 6379
      password: 123456
      host: 127.0.0.1

megalith:
  blog:
    auth-url: http://127.0.0.1:8081/inner
    search-url: http://127.0.0.1:8085/inner
    user-url: http://127.0.0.1:8086/inner
    register:
      page-prefix: http://127.0.0.1:1919/register/
    read:
      page-prefix: http://127.0.0.1:1919/blog/
    jwt:
      secret: ti8n3439n439t43ld9nv9343ddfer09hti8n3439n439t43ld9nv9343ddfer09ti8n3439n439t43ld9nv9343ddfer09fti8n3439n439t43ld9nv9343ddfer09f
    aliyun:
      oss:
        bucket-name: blog
      access-key-id: accessKeyId
      access-key-secret: accessKeySecret