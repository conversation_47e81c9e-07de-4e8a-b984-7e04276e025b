[package]
name = "micro-gateway-rs"
version = "0.1.0"
edition = "2024"

[dependencies]
axum = { version = "0.8.4", features = ["ws"] }
hyper = { version = "1.7.0", features = ["full"] }
tokio = { version = "1.47.1", features = ["full"] }
http-body-util = "0.1.3"
hyper-util = { version = "0.1.16", features = ["full"] }
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.143"
tracing = "0.1.41"
tracing-subscriber = "0.3.20"
tokio-tungstenite = "0.27.0"
futures-util = "0.3.31"
url = "2.5.7"
